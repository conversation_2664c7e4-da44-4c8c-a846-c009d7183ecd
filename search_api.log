2025-07-21 15:26:06,842 - __main__ - INFO - Starting up Search API...
2025-07-21 15:26:06,842 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-21 15:26:06,846 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-21 15:26:06,846 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-21 15:26:10,805 - __main__ - INFO - Model loaded successfully
2025-07-21 15:26:10,806 - __main__ - INFO - Initializing database connection...
2025-07-21 15:26:10,918 - __main__ - INFO - Database connection established
2025-07-21 15:26:10,920 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-21 15:26:10,920 - __main__ - INFO - Loading data with chunked processing and splitting...
2025-07-21 15:26:11,035 - __main__ - INFO - Total records to process: 75513
2025-07-21 15:26:11,037 - __main__ - INFO - Processing chunk 1: records 0-5000
2025-07-21 15:26:15,962 - __main__ - INFO - Progress: 5000/75513 records processed (6.6%)
2025-07-21 15:26:15,963 - __main__ - INFO - Processing chunk 2: records 5000-10000
2025-07-21 15:26:20,929 - __main__ - INFO - Processing chunk 3: records 10000-15000
2025-07-21 15:26:25,432 - __main__ - INFO - Processing chunk 4: records 15000-20000
2025-07-21 15:26:29,656 - __main__ - INFO - Processing chunk 5: records 20000-25000
2025-07-21 15:26:33,565 - __main__ - INFO - Processing chunk 6: records 25000-30000
2025-07-21 15:26:38,131 - __main__ - INFO - Progress: 30000/75513 records processed (39.7%)
2025-07-21 15:26:38,131 - __main__ - INFO - Processing chunk 7: records 30000-35000
2025-07-21 15:26:42,510 - __main__ - INFO - Processing chunk 8: records 35000-40000
2025-07-21 15:26:46,909 - __main__ - INFO - Processing chunk 9: records 40000-45000
2025-07-21 15:26:51,320 - __main__ - INFO - Processing chunk 10: records 45000-50000
2025-07-21 15:26:56,336 - __main__ - INFO - Processing chunk 11: records 50000-55000
2025-07-21 15:27:00,847 - __main__ - INFO - Progress: 55000/75513 records processed (72.8%)
2025-07-21 15:27:00,847 - __main__ - INFO - Processing chunk 12: records 55000-60000
2025-07-21 15:27:05,467 - __main__ - INFO - Processing chunk 13: records 60000-65000
2025-07-21 15:27:09,824 - __main__ - INFO - Processing chunk 14: records 65000-70000
2025-07-21 15:27:14,758 - __main__ - INFO - Processing chunk 15: records 70000-75000
2025-07-21 15:27:20,818 - __main__ - INFO - Processing chunk 16: records 75000-75513
2025-07-21 15:27:22,136 - __main__ - INFO - Progress: 75513/75513 records processed (100.0%)
2025-07-21 15:27:22,137 - __main__ - INFO - Saving BM25 index to disk: 160583 terms, 75513 documents
2025-07-21 15:27:23,472 - __main__ - INFO - BM25 index saved to disk
2025-07-21 15:27:23,650 - __main__ - INFO - GC: 0 objects collected, 8.8MB freed
2025-07-21 15:27:23,667 - __main__ - INFO - Data loading complete: 75513 records saved to search_cache
2025-07-21 15:27:23,733 - __main__ - INFO - Chunked cache loading completed in 72.81s
2025-07-21 15:27:23,734 - __main__ - INFO - Loaded 75513 records with memory-mapped storage
2025-07-21 15:27:23,909 - __main__ - INFO - Search cache loaded: 75513 records
2025-07-21 15:27:23,910 - __main__ - INFO - Memory-mapped storage: True
2025-07-21 15:27:34,068 - __main__ - INFO - Request 1753126054-1429398277536: GET http://localhost:8000/search?q=theft&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-21 15:27:34,070 - __main__ - INFO - Processing search request: 'theft' on table 'ReportNLP'
2025-07-21 15:27:34,071 - __main__ - INFO - Processing search: 'theft' (Memory: 504.0MB)
2025-07-21 15:27:34,526 - __main__ - INFO - Search completed: 20 results in 455.36ms (Memory: 504.0MB -> 661.7MB, diff: +157.7MB)
2025-07-21 15:27:34,692 - __main__ - WARNING - Request 1753126054-1429398277536 increased memory by 157.8MB (from 504.0MB to 661.8MB)
2025-07-21 15:27:34,844 - __main__ - INFO - Request 1753126054-1429398277536 completed in 623.99ms with status 200
2025-07-21 15:28:00,816 - __main__ - INFO - Request 1753126080-1429402467840: GET http://localhost:8000/search?q=domestic%20violence&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-21 15:28:00,818 - __main__ - INFO - Processing search request: 'domestic violence' on table 'ReportNLP'
2025-07-21 15:28:00,818 - __main__ - INFO - Processing search: 'domestic violence' (Memory: 661.8MB)
2025-07-21 15:28:01,218 - __main__ - INFO - Search completed: 20 results in 400.23ms (Memory: 661.8MB -> 662.0MB, diff: +0.2MB)
2025-07-21 15:28:01,221 - __main__ - INFO - Request 1753126080-1429402467840 completed in 405.24ms with status 200
2025-07-21 15:28:52,169 - __main__ - INFO - Request 1753126132-1429405012992: GET http://localhost:8000/search?q=violence&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-21 15:28:52,170 - __main__ - INFO - Processing search request: 'violence' on table 'ReportNLP'
2025-07-21 15:28:52,170 - __main__ - INFO - Processing search: 'violence' (Memory: 662.1MB)
2025-07-21 15:28:52,509 - __main__ - INFO - Search completed: 20 results in 339.42ms (Memory: 662.1MB -> 662.2MB, diff: +0.1MB)
2025-07-21 15:28:52,512 - __main__ - INFO - Request 1753126132-1429405012992 completed in 342.98ms with status 200
2025-07-21 15:29:10,483 - __main__ - INFO - Request 1753126150-1429405011120: GET http://localhost:8000/search?q=fight&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-21 15:29:10,483 - __main__ - INFO - Processing search request: 'fight' on table 'ReportNLP'
2025-07-21 15:29:10,484 - __main__ - INFO - Processing search: 'fight' (Memory: 662.2MB)
2025-07-21 15:29:10,818 - __main__ - INFO - Search completed: 20 results in 333.48ms (Memory: 662.2MB -> 662.6MB, diff: +0.4MB)
2025-07-21 15:29:10,820 - __main__ - INFO - Request 1753126150-1429405011120 completed in 338.05ms with status 200
2025-07-21 15:30:19,658 - __main__ - INFO - Request 1753126219-1429414504512: GET http://localhost:8000/memory
2025-07-21 15:30:19,659 - __main__ - INFO - Request 1753126219-1429414504512 completed in 1.00ms with status 200
2025-07-22 08:17:25,557 - __main__ - INFO - Request 1753186645-1429405049936: GET http://localhost:8000/memory
2025-07-22 08:17:25,559 - __main__ - INFO - Request 1753186645-1429405049936 completed in 2.00ms with status 200
2025-07-22 08:17:39,562 - __main__ - INFO - Request 1753186659-1429399870336: GET http://localhost:8000/search?q=vandalism&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-22 08:17:39,563 - __main__ - INFO - Processing search request: 'vandalism' on table 'ReportNLP'
2025-07-22 08:17:39,564 - __main__ - INFO - Processing search: 'vandalism' (Memory: 662.8MB)
2025-07-22 08:17:40,305 - __main__ - INFO - Search completed: 20 results in 741.81ms (Memory: 662.8MB -> 662.9MB, diff: +0.2MB)
2025-07-22 08:17:40,307 - __main__ - INFO - Request 1753186659-1429399870336 completed in 745.53ms with status 200
2025-07-22 08:24:19,960 - __main__ - INFO - Request 1753187059-1429403374256: GET http://localhost:8000/memory
2025-07-22 08:24:19,983 - __main__ - INFO - Request 1753187059-1429403374256 completed in 22.99ms with status 200
2025-07-22 08:24:32,139 - __main__ - INFO - Request 1753187072-1429405041872: GET http://localhost:8000/search?q=car&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-22 08:24:32,179 - __main__ - INFO - Processing search request: 'car' on table 'ReportNLP'
2025-07-22 08:24:32,199 - __main__ - INFO - Processing search: 'car' (Memory: 663.0MB)
2025-07-22 08:24:34,074 - __main__ - INFO - Search completed: 20 results in 1873.56ms (Memory: 663.0MB -> 663.0MB, diff: +0.0MB)
2025-07-22 08:24:34,124 - __main__ - INFO - Request 1753187072-1429405041872 completed in 1984.60ms with status 200
2025-07-22 08:24:49,232 - __main__ - INFO - Request 1753187089-1429399671712: GET http://localhost:8000/memory
2025-07-22 08:24:49,260 - __main__ - INFO - Request 1753187089-1429399671712 completed in 28.00ms with status 200
2025-07-22 08:25:12,366 - __main__ - INFO - Shutting down Search API...
2025-07-22 08:25:12,449 - __main__ - INFO - Database connection closed
2025-07-22 08:55:38,535 - __main__ - INFO - Starting up Search API...
2025-07-22 08:55:38,536 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-22 08:55:38,543 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-22 08:55:38,543 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-22 08:55:42,241 - __main__ - INFO - Model loaded successfully
2025-07-22 08:55:42,241 - __main__ - INFO - Initializing database connection...
2025-07-22 08:55:42,415 - __main__ - INFO - Database connection established
2025-07-22 08:55:42,415 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-22 08:55:42,416 - __main__ - INFO - All cache files found on disk
2025-07-22 08:55:42,417 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-22 08:55:42,450 - __main__ - INFO - Loading 75513 records from existing cache (110.6MB)
2025-07-22 08:55:42,470 - __main__ - INFO - Loading BM25 index from disk...
2025-07-22 08:55:43,324 - __main__ - INFO - BM25 index loaded: 160583 terms, 75513 documents
2025-07-22 08:55:43,324 - __main__ - INFO - Cache loaded from disk in 0.91s
2025-07-22 08:55:43,325 - __main__ - INFO - Loaded 75513 records with memory-mapped storage
2025-07-22 08:55:43,644 - __main__ - INFO - Search cache loaded: 75513 records
2025-07-22 08:55:43,645 - __main__ - INFO - Memory-mapped storage: True
2025-07-22 08:59:13,191 - __main__ - INFO - Shutting down Search API...
2025-07-22 08:59:13,194 - __main__ - INFO - Database connection closed
2025-07-22 09:01:40,721 - __main__ - INFO - Starting up Search API...
2025-07-22 09:01:40,721 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-22 09:01:40,724 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-22 09:01:40,725 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-22 09:01:43,660 - __main__ - INFO - Model loaded successfully
2025-07-22 09:01:43,660 - __main__ - INFO - Initializing database connection...
2025-07-22 09:01:43,793 - __main__ - INFO - Database connection established
2025-07-22 09:01:43,794 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-22 09:01:43,795 - __main__ - INFO - All cache files found on disk
2025-07-22 09:01:43,796 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-22 09:01:43,803 - __main__ - INFO - Loading 75513 records from existing cache (110.6MB)
2025-07-22 09:01:43,810 - __main__ - INFO - Loading BM25 index from disk...
2025-07-22 09:01:44,468 - __main__ - INFO - BM25 index loaded: 160583 terms, 75513 documents
2025-07-22 09:01:44,468 - __main__ - INFO - Cache loaded from disk in 0.67s
2025-07-22 09:01:44,468 - __main__ - INFO - Loaded 75513 records with memory-mapped storage
2025-07-22 09:01:44,762 - __main__ - INFO - Search cache loaded: 75513 records
2025-07-22 09:01:44,763 - __main__ - INFO - Memory-mapped storage: True
2025-07-22 09:01:44,763 - __main__ - INFO - Starting system monitor...
2025-07-22 09:01:44,765 - __main__ - INFO - System monitor started
2025-07-22 09:02:12,055 - __main__ - INFO - Request 1753189332-1913562778656: GET http://localhost:8000/search?q=car&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-22 09:02:12,056 - __main__ - INFO - Processing search request: 'car' on table 'ReportNLP'
2025-07-22 09:02:12,057 - __main__ - INFO - Processing search: 'car' (Memory: 478.7MB)
2025-07-22 09:02:12,922 - __main__ - INFO - Search completed: 20 results in 863.88ms (Memory: 478.7MB -> 636.4MB, diff: +157.7MB)
2025-07-22 09:02:13,100 - __main__ - WARNING - Request 1753189332-1913562778656 increased memory by 157.8MB (from 478.6MB to 636.5MB)
2025-07-22 09:02:13,266 - __main__ - INFO - Request 1753189332-1913562778656 completed in 1045.29ms with status 200
2025-07-22 09:03:01,019 - __main__ - INFO - Request 1753189381-1913562792928: GET http://localhost:8000/search?q=theft&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-22 09:03:01,020 - __main__ - INFO - Processing search request: 'theft' on table 'ReportNLP'
2025-07-22 09:03:01,020 - __main__ - INFO - Processing search: 'theft' (Memory: 636.5MB)
2025-07-22 09:03:01,708 - __main__ - INFO - Search completed: 20 results in 688.13ms (Memory: 636.5MB -> 636.6MB, diff: +0.2MB)
2025-07-22 09:03:01,710 - __main__ - INFO - Request 1753189381-1913562792928 completed in 691.13ms with status 200
2025-07-22 09:05:03,643 - __main__ - INFO - Shutting down Search API...
2025-07-22 09:05:04,649 - __main__ - INFO - System monitor stopped
2025-07-22 09:05:04,652 - __main__ - INFO - Database connection closed
2025-07-22 09:09:00,836 - __main__ - INFO - Starting up Search API...
2025-07-22 09:09:00,837 - __main__ - INFO - Loading SentenceTransformer model...
2025-07-22 09:09:00,843 - sentence_transformers.SentenceTransformer - INFO - Use pytorch device_name: cpu
2025-07-22 09:09:00,844 - sentence_transformers.SentenceTransformer - INFO - Load pretrained SentenceTransformer: all-MiniLM-L6-v2
2025-07-22 09:09:03,522 - __main__ - INFO - Model loaded successfully
2025-07-22 09:09:03,523 - __main__ - INFO - Initializing database connection...
2025-07-22 09:09:03,659 - __main__ - INFO - Database connection established
2025-07-22 09:09:03,659 - __main__ - INFO - Loading search cache with chunked processing...
2025-07-22 09:09:03,661 - __main__ - INFO - All cache files found on disk
2025-07-22 09:09:03,661 - __main__ - INFO - Existing cache files found, attempting to load from disk...
2025-07-22 09:09:03,680 - __main__ - INFO - Loading 75513 records from existing cache (110.6MB)
2025-07-22 09:09:03,696 - __main__ - INFO - Loading BM25 index from disk...
2025-07-22 09:09:04,413 - __main__ - INFO - BM25 index loaded: 160583 terms, 75513 documents
2025-07-22 09:09:04,413 - __main__ - INFO - Cache loaded from disk in 0.75s
2025-07-22 09:09:04,414 - __main__ - INFO - Loaded 75513 records with memory-mapped storage
2025-07-22 09:09:04,734 - __main__ - INFO - Search cache loaded: 75513 records
2025-07-22 09:09:04,735 - __main__ - INFO - Memory-mapped storage: True
2025-07-22 09:09:04,735 - __main__ - INFO - Starting system monitor...
2025-07-22 09:09:04,736 - __main__ - INFO - System monitor started
2025-07-22 09:09:22,925 - __main__ - INFO - Request 1753189762-2385650666864: GET http://localhost:8000/search?q=theft&top_k=20&semantic_weight=0.7&lexical_weight=0.3&min_similarity=0.1&table=ReportNLP
2025-07-22 09:09:22,927 - __main__ - INFO - Processing search request: 'theft' on table 'ReportNLP'
2025-07-22 09:09:22,927 - __main__ - INFO - Processing search: 'theft' (Memory: 477.8MB)
2025-07-22 09:09:23,712 - __main__ - INFO - Search completed: 20 results in 785.05ms (Memory: 477.8MB -> 636.1MB, diff: +158.3MB)
2025-07-22 09:09:23,872 - __main__ - WARNING - Request 1753189762-2385650666864 increased memory by 158.4MB (from 477.7MB to 636.2MB)
2025-07-22 09:09:24,041 - __main__ - INFO - Request 1753189762-2385650666864 completed in 946.36ms with status 200
2025-07-22 09:10:00,191 - __main__ - INFO - Shutting down Search API...
2025-07-22 09:10:01,199 - __main__ - INFO - System monitor stopped
2025-07-22 09:10:01,201 - __main__ - INFO - Database connection closed
